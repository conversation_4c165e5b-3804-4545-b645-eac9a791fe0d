import React from 'react';

import { ErrorBoundary } from '@/components/ErrorBoundary';
import SimplifiedStatisticsTable from '@/components/table/SimplifiedStatisticsTable';
import { DarkReaderProvider } from '@/components/ui/DarkReaderProvider';
import { ApiProvider } from '@/context/ApiContext';

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <DarkReaderProvider
        enabled
        config={{
          brightness: 150,
          contrast: 130,
          sepia: 0,
          grayscale: 0,
        }}
        autoDetectSystemTheme={false}
      >
        <ApiProvider>
          <div>
            <SimplifiedStatisticsTable />
          </div>
        </ApiProvider>
      </DarkReaderProvider>
    </ErrorBoundary>
  );
};

export default App;
