import { useCallback, useEffect, useMemo, useState } from 'react';

import { useAssetChartData } from '@/hooks/useAssetChartData';
import { useAssetData } from '@/hooks/useAssetData';
import {
  findBtcDataForSymbol,
  formatDate,
  processCryptoStatistics,
} from '@/utils/dataProcessors';
import { processStockStatistics } from '@/utils/stockDataProcessors';

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '@/generated';
import type { AssetType } from '@/types/table';
import type { AssetStatisticsDto } from '@/utils/assetTableFiltering';

interface UseTableDataManagerProps {
  assetType: AssetType;
}

interface TableDataManagerReturn {
  // Processed data
  processedData: AssetStatisticsDto[];
  btcStatistics: AssetStatisticsDto[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  error: string | null;

  // Chart data
  chartData: unknown;
  showChart: boolean;
  setShowChart: (show: boolean) => void;

  // Actions
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;

  // Utilities
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

/**
 * Process asset statistics based on asset type
 */
const useProcessedAssetData = (
  assetStatistics: AssetStatisticsDto[] | null,
  assetType: AssetType,
) => {
  return useMemo(() => {
    if (!assetStatistics || assetStatistics.length === 0) {
      return [];
    }

    return assetType === 'crypto'
      ? processCryptoStatistics(assetStatistics as CryptoCurrencyStatisticsDto[]).usdStatistics
      : processStockStatistics(assetStatistics as StockStatisticsDto[]).stockStatistics;
  }, [assetStatistics, assetType]);
};

/**
 * Process BTC statistics for both crypto and stock data
 */
const useBtcStatistics = (
  assetStatistics: AssetStatisticsDto[] | null,
  assetType: AssetType,
) => {
  return useMemo(() => {
    if (!assetStatistics || assetStatistics.length === 0) {
      return [];
    }

    return assetType === 'crypto'
      ? processCryptoStatistics(assetStatistics as CryptoCurrencyStatisticsDto[]).btcStatistics
      : processStockStatistics(assetStatistics as StockStatisticsDto[]).btcStatistics;
  }, [assetStatistics, assetType]);
};



/**
 * Unified hook that manages all table data logic for both crypto and stock
 * Now uses generic hooks to eliminate duplication
 */
export const useTableDataManager = ({
  assetType
}: UseTableDataManagerProps): TableDataManagerReturn => {
  const [showChart, setShowChart] = useState(false);

  // Generic hooks that work for both crypto and stock
  const { data: assetStatistics, loading, error, fetchData } = useAssetData(assetType);
  const { chartData, fetchChartData } = useAssetChartData(assetType);

  // Process data using extracted hooks
  const processedData = useProcessedAssetData(assetStatistics, assetType);
  const btcStatistics = useBtcStatistics(assetStatistics, assetType);

  // Signal click handler
  const onSignalClick = useCallback(async (symbol: string, currency: string) => {
    try {
      await fetchChartData(symbol, currency);
      setShowChart(true);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to handle signal click:', error);
    }
  }, [fetchChartData]);

  // Refresh handler - stable reference to prevent unnecessary re-renders
  const onRefresh = useCallback(() => {
    void fetchData();
  }, [fetchData]);

  // Initial data fetch effect - only when assetType changes
  useEffect(() => {
    void fetchData();
  }, [assetType]); // Only depend on assetType, not onRefresh

  // Auto-refresh effect - longer interval and stable dependencies
  useEffect(() => {
    const REFRESH_INTERVAL = 60_000; // 60 seconds (reduced frequency)
    const interval = setInterval(() => {
      void fetchData(); // Call fetchData directly to avoid dependency issues
    }, REFRESH_INTERVAL);

    return () => { clearInterval(interval); };
  }, [assetType]); // Only depend on assetType to prevent unnecessary interval resets

  return {
    // Processed data - return unfiltered data, let TableProvider handle filtering
    processedData,
    btcStatistics,
    totalCount: processedData.length,
    filteredCount: processedData.length, // Add this required property
    loading,
    error,

    // Chart data
    chartData: chartData as unknown,
    showChart,
    setShowChart,

    // Actions
    onSignalClick,
    onRefresh,

    // Utilities
    formatDate,
    findBtcDataForSymbol,
  };
};
