import { type MRT_ColumnDef } from 'material-react-table';

import { formatters } from '@/utils/formatters';

import { PriceCell, SignalCell, SymbolCell } from './TableCellComponents';
import type { TableRowData } from './types';

interface CreateColumnsProps {
  activeTab: string;
  formatDate: (timestamp: string) => string;
  onSignalClick: (symbol: string, currency: string) => void;
}

export const createColumns = ({
  activeTab,
  formatDate,
  onSignalClick,
}: CreateColumnsProps): MRT_ColumnDef<TableRowData>[] => [
  {
    accessorKey: 'symbol',
    header: activeTab === 'crypto' ? 'Cryptocurrency' : 'Stock',
    size: 150,
    Cell: ({ row }) => (
      <SymbolCell
        symbol={row.original.symbol}
        slug={row.original.slug}
        activeTab={activeTab}
      />
    ),
  },
  {
    accessorKey: 'usdPrice',
    header: 'USD Price',
    size: 120,
    Cell: ({ cell }) => (
      <PriceCell
        value={cell.getValue() as number}
        formatter={(value: number) => formatters.price(value, 'USD')}
      />
    ),
  },
  {
    accessorKey: 'marketCap',
    header: 'Market Cap',
    size: 120,
    Cell: ({ cell }) => (
      <PriceCell
        value={cell.getValue() as number}
        formatter={formatters.formatMarketCap}
      />
    ),
  },
  {
    accessorKey: 'usdSignal',
    header: 'USD Signal',
    size: 120,
    enableSorting: true,
    Cell: ({ row }) => (
      <SignalCell
        color={row.original.usdSignal}
        symbol={row.original.symbol}
        currency="USD"
        timestamp={row.original.usdTimestamp}
        formatDate={formatDate}
        onSignalClick={onSignalClick}
      />
    ),
  },
  {
    accessorKey: 'btcPrice',
    header: 'BTC Price',
    size: 120,
    Cell: ({ cell }) => (
      <PriceCell
        value={cell.getValue() as number}
        formatter={formatters.formatBtcPrice}
      />
    ),
  },
  {
    accessorKey: 'btcSignal',
    header: 'BTC Signal',
    size: 120,
    enableSorting: true,
    Cell: ({ row }) => (
      <SignalCell
        color={row.original.btcSignal}
        symbol={row.original.symbol}
        currency={activeTab === 'crypto' ? 'BTC' : row.original.conversionCurrency}
        timestamp={row.original.btcTimestamp}
        formatDate={formatDate}
        onSignalClick={onSignalClick}
      />
    ),
  },
];
