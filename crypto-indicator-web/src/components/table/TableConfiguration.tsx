import { type MRT_TableOptions } from 'material-react-table';

import type { TableRowData } from './types';

const BG_PRIMARY_RGB = 'var(--bg-primary-rgb)';
const BG_SURFACE_RGB = 'var(--bg-surface-rgb)';
const BG_SECONDARY_RGB = 'var(--bg-secondary-rgb)';
const BG_ELEVATED_RGB = 'var(--bg-elevated-rgb)';
const TEXT_PRIMARY = 'var(--text-primary)';
const BORDER_PRIMARY = 'var(--border-primary)';
const BORDER_PRIMARY_RGB = 'var(--border-primary-rgb)';
const BORDER_HOVER = 'var(--border-hover)';
const ACCENT_PRIMARY = 'var(--accent-primary)';
const _ACCENT_PRIMARY_RGB = 'var(--accent-primary-rgb)';
const GLASS_OPACITY_PRIMARY = 'var(--glass-opacity-primary)';
const GLASS_BLUR_PRIMARY = 'var(--glass-blur-primary)';
const GLASS_BORDER_PRIMARY = 'var(--glass-border-primary)';
const GLASS_SHADOW_PRIMARY = 'var(--glass-shadow-primary)';

export const getTableConfiguration = (
  activeTab: string,
  loading: boolean
): Partial<MRT_TableOptions<TableRowData>> => ({
  enableSorting: true,
  enableColumnFilters: true,
  enableGlobalFilter: true,
  enableRowSelection: false,
  enablePagination: false,
  enableBottomToolbar: false,
  enableTopToolbar: true,
  enableStickyHeader: true,
  enableColumnResizing: true,
  enableColumnOrdering: true,
  enableDensityToggle: true,
  enableFullScreenToggle: true,
  enableHiding: true,
  
  // Responsive design
  muiTableContainerProps: {
    sx: { 
      maxHeight: '75vh',
      // Apply glassmorphism styling
      background: `rgba(${BG_PRIMARY_RGB}, ${GLASS_OPACITY_PRIMARY})`,
      backdropFilter: GLASS_BLUR_PRIMARY,
      border: GLASS_BORDER_PRIMARY,
      borderRadius: '24px',
      boxShadow: GLASS_SHADOW_PRIMARY,
    },
  },
  
  // Table styling
  muiTablePaperProps: {
    sx: {
      background: 'transparent',
      boxShadow: 'none',
    },
  },
  
  // Header styling
  muiTableHeadCellProps: {
    sx: {
      backgroundColor: `rgba(${BG_SURFACE_RGB}, 0.8)`,
      color: TEXT_PRIMARY,
      fontWeight: 'bold',
      fontSize: {
        xs: '0.75rem',
        sm: '0.8rem',
        md: '0.875rem',
      },
      borderBottom: `1px solid ${BORDER_PRIMARY}`,
    },
  },
  
  // Row styling
  muiTableBodyRowProps: {
    sx: {
      backgroundColor: `rgba(${BG_PRIMARY_RGB}, 0.6)`,
      '&:hover': {
        backgroundColor: `rgba(${BG_ELEVATED_RGB}, 0.8)`,
      },
      '&:nth-of-type(odd)': {
        backgroundColor: `rgba(${BG_SECONDARY_RGB}, 0.4)`,
      },
    },
  },
  
  // Cell styling
  muiTableBodyCellProps: {
    sx: {
      color: TEXT_PRIMARY,
      borderBottom: `1px solid rgba(${BORDER_PRIMARY_RGB}, 0.3)`,
      fontSize: {
        xs: '0.75rem',
        sm: '0.8rem',
        md: '0.875rem',
      },
    },
  },
  
  // Toolbar styling
  muiTopToolbarProps: {
    sx: {
      backgroundColor: `rgba(${BG_SURFACE_RGB}, 0.9)`,
      borderBottom: `1px solid ${BORDER_PRIMARY}`,
      borderRadius: '24px 24px 0 0',
    },
  },
  
  // Search field styling
  muiSearchTextFieldProps: {
    placeholder: `Search ${activeTab === 'crypto' ? 'cryptocurrencies' : 'stocks'}...`,
    variant: 'outlined',
    size: 'small',
    sx: {
      '& .MuiOutlinedInput-root': {
        backgroundColor: `rgba(${BG_PRIMARY_RGB}, 0.8)`,
        color: TEXT_PRIMARY,
        '& fieldset': {
          borderColor: BORDER_PRIMARY,
        },
        '&:hover fieldset': {
          borderColor: BORDER_HOVER,
        },
        '&.Mui-focused fieldset': {
          borderColor: ACCENT_PRIMARY,
        },
      },
    },
  },
  
  // Loading state
  state: {
    isLoading: loading,
    showProgressBars: loading,
  },
  
  // Initial state
  initialState: {
    density: 'compact',
    showGlobalFilter: true,
    columnPinning: {
      left: ['symbol'],
    },
  },
});
