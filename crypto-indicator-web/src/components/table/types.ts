import type { CryptoCurrencyStatisticsDto, StockStatisticsDto } from '@/generated';

export interface TableRowData {
  symbol: string;
  name: string;
  slug?: string | undefined;
  usdPrice: number;
  marketCap: number;
  usdSignal: string;
  usdTimestamp?: string | undefined;
  btcPrice: number;
  btcSignal: string;
  btcTimestamp?: string | undefined;
  conversionCurrency: string;
  originalData: CryptoCurrencyStatisticsDto | StockStatisticsDto;
}
