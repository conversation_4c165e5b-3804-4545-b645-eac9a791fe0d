import React, { useMemo } from 'react';
import {
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef,
} from 'material-react-table';
import { Box, Button, Chip, IconButton } from '@mui/material';
import { Launch as LaunchIcon } from '@mui/icons-material';

import { AppStateHandler } from '@/components/AppStateHandler';
import { ChartModalStates } from '@/components/chart/ChartModalStates';
import { DashboardHeader } from '@/components/ui/DashboardHeader';
import { TabNavigation } from '@/components/ui/TabNavigation';
import { SignalBadge } from '@/components/signals/SignalBadge';
import { CSS_CLASSES } from '@/constants/app';
import { useTableDataManager } from '@/hooks/useTableDataManager';
import { useTabNavigation } from '@/hooks/useTabNavigation';
import { DEFAULT_TABS } from '@/types/tabs';
import { formatters, navigation } from '@/utils/formatters';

import type { 
  CryptoCurrencyStatisticsDto, 
  StockStatisticsDto, 
  IndicatorValueDto 
} from '@/generated';

// Component-specific styles
import '../../styles/components/table.css';

type TableData = CryptoCurrencyStatisticsDto | StockStatisticsDto;

const SimplifiedStatisticsTable: React.FC = () => {
  // Tab navigation
  const { activeTab, setActiveTab } = useTabNavigation('crypto');

  // Get data for AppStateHandler and ChartModal
  const {
    processedData,
    btcStatistics,
    totalCount,
    loading,
    error,
    chartData,
    showChart,
    setShowChart,
    onSignalClick,
    onRefresh,
    formatDate,
    findBtcDataForSymbol,
  } = useTableDataManager({ assetType: activeTab });

  // Transform data for the table
  const tableData = useMemo(() => {
    return processedData.map((item) => {
      const usdData = item.indicatorValues.find(Boolean);
      const btcData = findBtcDataForSymbol(btcStatistics, item.symbol);
      
      return {
        symbol: item.symbol,
        name: 'mapping' in item ? item.mapping?.name || item.symbol : item.symbol,
        slug: 'mapping' in item && 'slug' in item.mapping ? item.mapping.slug : undefined,
        usdPrice: usdData?.close || 0,
        marketCap: usdData?.marketCap || 0,
        usdSignal: usdData?.color || 'gray',
        usdTimestamp: usdData?.timestamp,
        btcPrice: btcData?.close || 0,
        btcSignal: btcData?.color || 'gray',
        btcTimestamp: btcData?.timestamp,
        conversionCurrency: item.conversionCurrency,
        originalData: item,
      };
    });
  }, [processedData, btcStatistics, findBtcDataForSymbol]);

  // Define columns
  const columns = useMemo<MRT_ColumnDef<typeof tableData[0]>[]>(() => [
    {
      accessorKey: 'symbol',
      header: activeTab === 'crypto' ? 'Cryptocurrency' : 'Stock',
      size: 150,
      Cell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Button
            variant="text"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              if (activeTab === 'crypto' && row.original.slug) {
                navigation.openCoinMarketCap(row.original.slug);
              } else {
                navigation.openYahoo(row.original.symbol);
              }
            }}
            sx={{ 
              minWidth: 'auto',
              p: 0.5,
              fontSize: '0.875rem',
              fontWeight: 'bold',
              color: 'var(--text-primary)',
              '&:hover': {
                backgroundColor: 'rgba(var(--accent-primary-rgb), 0.1)',
              }
            }}
            endIcon={<LaunchIcon sx={{ fontSize: '0.75rem' }} />}
          >
            {row.original.symbol}
          </Button>
        </Box>
      ),
    },
    {
      accessorKey: 'usdPrice',
      header: 'USD Price',
      size: 120,
      Cell: ({ cell }) => (
        <Box sx={{ fontWeight: 'bold', color: 'var(--text-primary)' }}>
          {formatters.formatPrice(cell.getValue<number>())}
        </Box>
      ),
    },
    {
      accessorKey: 'marketCap',
      header: 'Market Cap',
      size: 120,
      Cell: ({ cell }) => (
        <Box sx={{ fontWeight: 'bold', color: 'var(--text-primary)' }}>
          {formatters.formatMarketCap(cell.getValue<number>())}
        </Box>
      ),
    },
    {
      accessorKey: 'usdSignal',
      header: 'USD Signal',
      size: 120,
      enableSorting: true,
      Cell: ({ row }) => (
        <SignalBadge
          color={row.original.usdSignal}
          clickable={true}
          title={`Click to view chart${
            row.original.usdTimestamp 
              ? ` • Last update: ${formatDate(row.original.usdTimestamp)}`
              : ''
          }`}
          onClick={() => onSignalClick(row.original.symbol, 'USD')}
        />
      ),
    },
    {
      accessorKey: 'btcPrice',
      header: 'BTC Price',
      size: 120,
      Cell: ({ cell }) => (
        <Box sx={{ fontWeight: 'bold', color: 'var(--text-primary)' }}>
          {formatters.formatBtcPrice(cell.getValue<number>())}
        </Box>
      ),
    },
    {
      accessorKey: 'btcSignal',
      header: 'BTC Signal',
      size: 120,
      enableSorting: true,
      Cell: ({ row }) => (
        <SignalBadge
          color={row.original.btcSignal}
          clickable={true}
          title={`Click to view chart${
            row.original.btcTimestamp 
              ? ` • Last update: ${formatDate(row.original.btcTimestamp)}`
              : ''
          }`}
          onClick={() => onSignalClick(
            row.original.symbol, 
            activeTab === 'crypto' ? 'BTC' : row.original.conversionCurrency
          )}
        />
      ),
    },
  ], [activeTab, formatDate, onSignalClick]);

  // Configure the table
  const table = useMaterialReactTable({
    columns,
    data: tableData,
    enableSorting: true,
    enableColumnFilters: true,
    enableGlobalFilter: true,
    enableRowSelection: false,
    enablePagination: false,
    enableBottomToolbar: false,
    enableTopToolbar: true,
    enableStickyHeader: true,
    enableColumnResizing: true,
    enableColumnOrdering: true,
    enableDensityToggle: true,
    enableFullScreenToggle: true,
    enableHiding: true,
    
    // Responsive design
    muiTableContainerProps: {
      sx: { 
        maxHeight: '75vh',
        // Apply glassmorphism styling
        background: 'rgba(var(--bg-primary-rgb), var(--glass-opacity-primary))',
        backdropFilter: 'var(--glass-blur-primary)',
        border: 'var(--glass-border-primary)',
        borderRadius: '24px',
        boxShadow: 'var(--glass-shadow-primary)',
      },
    },
    
    // Table styling
    muiTablePaperProps: {
      sx: {
        background: 'transparent',
        boxShadow: 'none',
      },
    },
    
    // Header styling
    muiTableHeadCellProps: {
      sx: {
        backgroundColor: 'rgba(var(--bg-surface-rgb), 0.8)',
        color: 'var(--text-primary)',
        fontWeight: 'bold',
        fontSize: {
          xs: '0.75rem',
          sm: '0.8rem',
          md: '0.875rem',
        },
        borderBottom: '1px solid var(--border-primary)',
      },
    },
    
    // Row styling
    muiTableBodyRowProps: {
      sx: {
        backgroundColor: 'rgba(var(--bg-primary-rgb), 0.6)',
        '&:hover': {
          backgroundColor: 'rgba(var(--bg-elevated-rgb), 0.8)',
        },
        '&:nth-of-type(odd)': {
          backgroundColor: 'rgba(var(--bg-secondary-rgb), 0.4)',
        },
      },
    },
    
    // Cell styling
    muiTableBodyCellProps: {
      sx: {
        color: 'var(--text-primary)',
        borderBottom: '1px solid rgba(var(--border-primary-rgb), 0.3)',
        fontSize: {
          xs: '0.75rem',
          sm: '0.8rem',
          md: '0.875rem',
        },
      },
    },
    
    // Toolbar styling
    muiTopToolbarProps: {
      sx: {
        backgroundColor: 'rgba(var(--bg-surface-rgb), 0.9)',
        borderBottom: '1px solid var(--border-primary)',
        borderRadius: '24px 24px 0 0',
      },
    },
    
    // Search field styling
    muiSearchTextFieldProps: {
      placeholder: `Search ${activeTab === 'crypto' ? 'cryptocurrencies' : 'stocks'}...`,
      variant: 'outlined',
      size: 'small',
      sx: {
        '& .MuiOutlinedInput-root': {
          backgroundColor: 'rgba(var(--bg-primary-rgb), 0.8)',
          color: 'var(--text-primary)',
          '& fieldset': {
            borderColor: 'var(--border-primary)',
          },
          '&:hover fieldset': {
            borderColor: 'var(--border-hover)',
          },
          '&.Mui-focused fieldset': {
            borderColor: 'var(--accent-primary)',
          },
        },
      },
    },
    
    // Custom toolbar actions
    renderTopToolbarCustomActions: () => (
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
        <Chip
          label={`${tableData.length} ${activeTab === 'crypto' ? 'cryptocurrencies' : 'stocks'}`}
          size="small"
          sx={{
            backgroundColor: 'rgba(var(--accent-primary-rgb), 0.2)',
            color: 'var(--accent-primary)',
            fontWeight: 'bold',
          }}
        />
        <Button
          variant="outlined"
          size="small"
          onClick={onRefresh}
          disabled={loading}
          sx={{
            borderColor: 'var(--border-primary)',
            color: 'var(--text-primary)',
            '&:hover': {
              borderColor: 'var(--border-hover)',
              backgroundColor: 'rgba(var(--accent-primary-rgb), 0.1)',
            },
          }}
        >
          Refresh
        </Button>
      </Box>
    ),
    
    // Loading state
    state: {
      isLoading: loading,
      showProgressBars: loading,
    },
    
    // Initial state
    initialState: {
      density: 'compact',
      showGlobalFilter: true,
      columnPinning: {
        left: ['symbol'],
      },
    },
  });

  return (
    <AppStateHandler
      loading={loading}
      error={error}
      statisticsLength={processedData.length}
      onRetry={onRefresh}
    >
      <div className={CSS_CLASSES.APP_CONTAINER}>
        <DashboardHeader />

        <TabNavigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
          tabs={DEFAULT_TABS}
        />

        <Box sx={{ p: 2 }}>
          <MaterialReactTable table={table} />
        </Box>

        <ChartModalStates
          showChart={showChart}
          chartData={chartData as CryptoCurrencyStatisticsDto | StockStatisticsDto | null}
          chartLoading={false}
          chartError={null}
          onClose={() => { setShowChart(false); }}
        />
      </div>
    </AppStateHandler>
  );
};

export default SimplifiedStatisticsTable;
