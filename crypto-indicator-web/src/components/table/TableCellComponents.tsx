import React from 'react';
import { Box, Button } from '@mui/material';
import { Launch as LaunchIcon } from '@mui/icons-material';

import { SignalBadge } from '@/components/signals/SignalBadge';
import { formatters, navigation } from '@/utils/formatters';

const TEXT_PRIMARY = 'var(--text-primary)';
const ACCENT_PRIMARY_RGB = 'var(--accent-primary-rgb)';

interface SymbolCellProps {
  symbol: string;
  slug?: string;
  activeTab: string;
}

export const SymbolCell: React.FC<SymbolCellProps> = ({ symbol, slug, activeTab }) => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
    <Button
      variant="text"
      size="small"
      onClick={(e) => {
        e.stopPropagation();
        if (activeTab === 'crypto' && slug) {
          navigation.openCoinMarketCap(slug);
        } else {
          navigation.openYahoo(symbol);
        }
      }}
      sx={{ 
        minWidth: 'auto',
        p: 0.5,
        fontSize: '0.875rem',
        fontWeight: 'bold',
        color: TEXT_PRIMARY,
        '&:hover': {
          backgroundColor: `rgba(${ACCENT_PRIMARY_RGB}, 0.1)`,
        }
      }}
      endIcon={<LaunchIcon sx={{ fontSize: '0.75rem' }} />}
    >
      {symbol}
    </Button>
  </Box>
);

interface PriceCellProps {
  value: number;
  formatter: (value: number) => string;
}

export const PriceCell: React.FC<PriceCellProps> = ({ value, formatter }) => (
  <Box sx={{ fontWeight: 'bold', color: TEXT_PRIMARY }}>
    {formatter(value)}
  </Box>
);

interface SignalCellProps {
  color: string;
  symbol: string;
  currency: string;
  timestamp?: string;
  formatDate: (timestamp: string) => string;
  onSignalClick: (symbol: string, currency: string) => void;
}

export const SignalCell: React.FC<SignalCellProps> = ({ 
  color, 
  symbol, 
  currency, 
  timestamp, 
  formatDate, 
  onSignalClick 
}) => (
  <SignalBadge
    color={color}
    clickable
    title={`Click to view chart${
      timestamp 
        ? ` • Last update: ${formatDate(timestamp)}`
        : ''
    }`}
    onClick={() => { onSignalClick(symbol, currency); }}
  />
);
